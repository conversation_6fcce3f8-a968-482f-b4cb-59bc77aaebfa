<?php
/**
 * Blog Controller
 * Handles blog functionality
 */
class BlogController
{
    /**
     * Display the blog index page
     */
    public function index()
    {
        $categories = ['All', 'Architecture', 'Interior Design', 'Construction', 'Tips & Advice'];
        
        $posts = [
            [
                'id' => 1,
                'image' => 'blog-1.jpg',
                'title' => 'Tren Desain Arsitektur 2025',
                'excerpt' => 'Mengenal lebih dekat tren desain arsitektur yang akan populer di tahun 2025. Dari penggunaan material ramah lingkungan hingga desain biophilic yang mengintegrasikan elemen alam.',
                'date' => '1 Juni 2025',
                'author' => 'Ir. Anton Wijaya',
                'category' => 'Architecture',
                'tags' => ['trends', 'architecture', '2025']
            ],
            [
                'id' => 2,
                'image' => 'blog-2.jpg',
                'title' => 'Tips Merenovasi Rumah dengan Budget Terbatas',
                'excerpt' => 'Panduan praktis untuk merenovasi rumah tanpa menghabiskan banyak biaya. Fokus pada perubahan yang memberikan dampak visual maksimal dengan biaya minimal.',
                'date' => '25 Mei 2025',
                'author' => 'Sari Utami, M.Arch',
                'category' => 'Tips & Advice',
                'tags' => ['renovation', 'budget', 'tips']
            ],
            [
                'id' => 3,
                'image' => 'blog-3.jpg',
                'title' => 'Desain Rumah Tropis Modern',
                'excerpt' => 'Mengadaptasi gaya tropis ke dalam desain rumah modern yang nyaman. Memanfaatkan ventilasi silang, ruang terbuka, dan material lokal untuk menciptakan hunian yang sejuk dan hemat energi.',
                'date' => '15 Mei 2025',
                'author' => 'Ir. Anton Wijaya',
                'category' => 'Architecture',
                'tags' => ['tropical', 'modern', 'design']
            ],
            [
                'id' => 4,
                'image' => 'blog-4.jpg',
                'title' => 'Pentingnya Kontraktor Berpengalaman',
                'excerpt' => 'Mengapa memilih kontraktor berpengalaman sangat penting dalam proyek konstruksi. Menghindari masalah umum dan memastikan kualitas bangunan yang terjamin.',
                'date' => '5 Mei 2025',
                'author' => 'Budi Santoso, S.T.',
                'category' => 'Construction',
                'tags' => ['contractor', 'construction', 'quality']
            ],
            [
                'id' => 5,
                'image' => 'blog-5.jpg',
                'title' => 'Menciptakan Ruang Kerja yang Produktif',
                'excerpt' => 'Tips mendesain ruang kerja di rumah yang meningkatkan produktivitas dan kenyamanan. Memadukan ergonomi, pencahayaan, dan psikologi warna untuk workspace optimal.',
                'date' => '28 April 2025',
                'author' => 'Maya Indriani, S.Ds.',
                'category' => 'Interior Design',
                'tags' => ['workspace', 'productivity', 'interior']
            ],
            [
                'id' => 6,
                'image' => 'blog-6.jpg',
                'title' => 'Material Inovatif dalam Dunia Arsitektur',
                'excerpt' => 'Eksplorasi material-material baru dan inovatif yang mengubah cara kita membangun. Dari beton yang menyerap polusi hingga kaca pintar yang mengatur cahaya.',
                'date' => '15 April 2025',
                'author' => 'Sari Utami, M.Arch',
                'category' => 'Architecture',
                'tags' => ['materials', 'innovation', 'sustainability']
            ]
        ];
        
        view('pages.blog', [
            'categories' => $categories,
            'posts' => $posts
        ]);
    }
    
    /**
     * Display a single blog post
     */
    public function show()
    {
        // Get post ID from query string
        $postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        // In a real application, this would come from a database
        $posts = [
            1 => [
                'id' => 1,
                'image' => 'blog-1.jpg',
                'title' => 'Tren Desain Arsitektur 2025',
                'content' => '<p class="mb-4">Dunia arsitektur terus berkembang dengan inovasi dan ide-ide baru. Tahun 2025 membawa beberapa tren menarik yang menggabungkan keberlanjutan, teknologi, dan desain yang berpusat pada manusia.</p>
                <h3 class="text-xl font-bold mb-2 mt-6">1. Arsitektur Regeneratif</h3>
                <p class="mb-4">Bergerak melampaui konsep "keberlanjutan", arsitektur regeneratif bertujuan untuk menciptakan bangunan yang benar-benar memberikan dampak positif bagi lingkungan. Bangunan tidak hanya mengurangi dampak negatif, tetapi juga aktif memperbaiki ekosistem di sekitarnya.</p>
                <p class="mb-4">Contohnya termasuk bangunan yang menghasilkan lebih banyak energi daripada yang dikonsumsi, sistem pengolahan air yang memurnikan air hujan, dan fasad yang menyerap polusi udara.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">2. Biophilic Design</h3>
                <p class="mb-4">Desain biophilic yang mengintegrasikan alam ke dalam lingkungan buatan semakin populer. Ini melibatkan penggunaan pencahayaan alami, ventilasi, tanaman dalam ruangan, material alami, dan tampilan ke ruang hijau.</p>
                <p class="mb-4">Penelitian menunjukkan bahwa koneksi dengan alam meningkatkan kesejahteraan, produktivitas, dan kesehatan mental penghuni bangunan.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">3. Modular dan Prefabrikasi</h3>
                <p class="mb-4">Pembangunan modular dan prefabrikasi terus mendapatkan momentum karena efisiensi, pengurangan limbah, dan kemampuan untuk menciptakan bangunan berkualitas tinggi dengan biaya lebih rendah.</p>
                <p class="mb-4">Teknologi baru memungkinkan komponen prefabrikasi menjadi lebih fleksibel dan dapat disesuaikan, mengatasi kritik lama bahwa bangunan prefab terlihat terlalu seragam.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">4. Smart Buildings</h3>
                <p class="mb-4">Bangunan pintar yang menggunakan Internet of Things (IoT) untuk memantau dan mengoptimalkan berbagai sistem seperti HVAC, pencahayaan, dan keamanan semakin menjadi standar.</p>
                <p class="mb-4">Inovasi terbaru termasuk sistem yang mempelajari preferensi penghuni dan menyesuaikan lingkungan secara otomatis, menghemat energi sekaligus meningkatkan kenyamanan.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">5. Desain yang Berfokus pada Kesehatan</h3>
                <p class="mb-4">Pasca pandemi, ada perhatian yang lebih besar pada bagaimana bangunan memengaruhi kesehatan penghuninya. Ini mencakup peningkatan ventilasi, material yang minim VOC, pencahayaan yang selaras dengan ritme sirkadian, dan ruang yang mendorong aktivitas fisik.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">Kesimpulan</h3>
                <p class="mb-4">Tren arsitektur 2025 mencerminkan kebutuhan yang berkembang untuk bangunan yang tidak hanya indah dan fungsional, tetapi juga sehat, berkelanjutan, dan cerdas. Sebagai arsitek dan pengembang, mengadopsi tren ini tidak hanya akan membuat proyek kita lebih relevan secara komersial, tetapi juga berkontribusi pada masa depan yang lebih baik untuk planet kita.</p>',
                'date' => '1 Juni 2025',
                'author' => 'Ir. Anton Wijaya',
                'category' => 'Architecture',
                'tags' => ['trends', 'architecture', '2025']
            ],
            2 => [
                'id' => 2,
                'image' => 'blog-2.jpg',
                'title' => 'Tips Merenovasi Rumah dengan Budget Terbatas',
                'content' => '<p class="mb-4">Merenovasi rumah sering kali dianggap sebagai proyek yang mahal dan menguras tabungan. Namun, dengan perencanaan yang cermat dan strategi yang tepat, Anda bisa menyegarkan tampilan rumah tanpa harus mengeluarkan biaya besar.</p>
                <h3 class="text-xl font-bold mb-2 mt-6">1. Rencanakan dengan Matang</h3>
                <p class="mb-4">Sebelum memulai renovasi, buatlah rencana yang detail. Identifikasi area yang benar-benar perlu diperbaiki dan mana yang hanya perlu penyegaran ringan. Prioritaskan pekerjaan berdasarkan urgensi dan dampaknya terhadap kenyamanan dan nilai rumah.</p>
                <p class="mb-4">Membuat anggaran terperinci dan menambahkan buffer 10-20% untuk biaya tak terduga sangat penting untuk menghindari pengeluaran berlebihan.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">2. Cat Baru untuk Perubahan Dramatis</h3>
                <p class="mb-4">Mengubah warna dinding adalah cara paling ekonomis untuk memberikan tampilan baru pada ruangan. Pilihlah warna netral untuk area yang luas dan tambahkan aksen warna berani pada satu dinding atau detail arsitektural untuk menciptakan focal point.</p>
                <p class="mb-4">Jangan lupa untuk mengecat trim, pintu, dan plafon jika diperlukan, karena area ini sering kali terabaikan namun memiliki dampak visual yang signifikan.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">3. Perbarui Hardware dan Fixture</h3>
                <p class="mb-4">Mengganti handle lemari, keran, lampu, dan hardware lainnya adalah investasi kecil yang memberikan dampak besar. Hardware yang usang dapat membuat dapur atau kamar mandi terlihat ketinggalan zaman, sedangkan model yang baru dapat memberikan tampilan kontemporer dengan biaya minimal.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">4. Refacing vs Replacing</h3>
                <p class="mb-4">Daripada mengganti kabinet dapur atau lemari built-in secara keseluruhan, pertimbangkan untuk melakukan refacing—mengganti pintu dan drawer front saja sambil mempertahankan struktur utama. Ini bisa menghemat hingga 50-70% dari biaya penggantian total.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">5. DIY yang Bijak</h3>
                <p class="mb-4">Menangani beberapa pekerjaan sendiri dapat menghemat biaya tenaga kerja yang signifikan. Namun, kenali batas kemampuan Anda. Pekerjaan seperti pengecatan, pemasangan backsplash sederhana, atau penggantian hardware adalah proyek DIY yang layak, sedangkan listrik, pipa, dan struktural sebaiknya diserahkan kepada profesional.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">6. Perhatikan Pencahayaan</h3>
                <p class="mb-4">Meningkatkan pencahayaan bisa mengubah atmosfer ruangan secara dramatis tanpa renovasi besar. Pertimbangkan untuk menambahkan lampu task lighting di dapur, lampu meja di ruang keluarga, atau penerangan tidak langsung (indirect lighting) untuk menciptakan ambiance yang hangat.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">7. Gunakan Kembali dan Daur Ulang</h3>
                <p class="mb-4">Jelajahi toko barang bekas, lelang, atau marketplace online untuk menemukan furnitur dan material berkualitas dengan harga terjangkau. Seringkali barang-barang vintage atau bekas memiliki kualitas lebih baik daripada produk baru dengan harga sama.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">Kesimpulan</h3>
                <p class="mb-4">Renovasi dengan budget terbatas memang memerlukan lebih banyak kreativitas dan perencanaan, tetapi hasilnya bisa sangat memuaskan. Fokus pada perubahan yang memberikan dampak visual maksimal, dan jangan takut untuk menunda proyek yang bisa menunggu hingga dana lebih tersedia.</p>',
                'date' => '25 Mei 2025',
                'author' => 'Sari Utami, M.Arch',
                'category' => 'Tips & Advice',
                'tags' => ['renovation', 'budget', 'tips']
            ],
            3 => [
                'id' => 3,
                'image' => 'blog-3.jpg',
                'title' => 'Desain Rumah Tropis Modern',
                'content' => '<p class="mb-4">Indonesia dengan iklim tropisnya memerlukan pendekatan desain arsitektur yang khas untuk menciptakan hunian yang nyaman. Desain rumah tropis modern menggabungkan prinsip tradisional yang teruji waktu dengan estetika kontemporer dan teknologi terkini.</p>
                <h3 class="text-xl font-bold mb-2 mt-6">1. Memahami Iklim Tropis</h3>
                <p class="mb-4">Iklim tropis Indonesia ditandai dengan suhu tinggi sepanjang tahun, kelembaban tinggi, dan curah hujan yang signifikan. Desain yang efektif harus merespons kondisi ini dengan menciptakan rumah yang sejuk secara alami, terlindung dari hujan lebat, dan tahan terhadap kelembaban.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">2. Orientasi dan Tata Letak</h3>
                <p class="mb-4">Orientasi bangunan yang tepat sangat penting dalam desain tropis. Idealnya, rumah diposisikan untuk meminimalkan paparan matahari langsung dari timur dan barat, yang dapat menyebabkan pemanasan berlebihan.</p>
                <p class="mb-4">Tata letak yang mengoptimalkan ventilasi silang, dengan bukaan yang strategis pada arah angin dominan, membantu menciptakan aliran udara alami di seluruh rumah.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">3. Atap dan Teritisan</h3>
                <p class="mb-4">Atap miring dengan kemiringan minimal 30 derajat efektif untuk mengalirkan air hujan dengan cepat. Teritisan atau overhang yang lebar melindungi dinding dan jendela dari sinar matahari langsung dan tampias hujan.</p>
                <p class="mb-4">Dalam desain modern, bentuk atap dapat diinterpretasikan secara kreatif sambil tetap mempertahankan fungsi utamanya.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">4. Ventilasi Maksimal</h3>
                <p class="mb-4">Jendela besar, pintu jalusi, dan bukaan lainnya memaksimalkan aliran udara. Desain dengan ruang terbuka seperti void atau courtyard menciptakan efek cerobong yang mendorong pergerakan udara vertikal.</p>
                <p class="mb-4">Teknologi modern seperti jendela high-performance dengan lapisan low-e memungkinkan masuknya cahaya alami sambil meminimalkan panas.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">5. Material Lokal dan Berkelanjutan</h3>
                <p class="mb-4">Material lokal seperti kayu, bambu, dan batu alam tidak hanya berkelanjutan tetapi juga memiliki sifat termal yang menguntungkan untuk iklim tropis. Material ini dapat diintegrasikan ke dalam desain modern untuk menciptakan kesan hangat dan autentik.</p>
                <p class="mb-4">Beton ekspos, saat dikombinasikan dengan insulasi yang tepat, juga dapat berfungsi baik di iklim tropis, menawarkan massa termal yang membantu menstabilkan suhu interior.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">6. Ruang Indoor-Outdoor</h3>
                <p class="mb-4">Batas antara ruang dalam dan luar yang kabur adalah ciri khas desain tropis modern. Teras luas, beranda, dan lanai menciptakan ruang transisi yang dapat dinikmati sepanjang tahun.</p>
                <p class="mb-4">Lansekap yang terintegrasi dengan vegetasi yang tepat tidak hanya menambah nilai estetika tetapi juga membantu mendinginkan area sekitar rumah.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">7. Pencahayaan Alami</h3>
                <p class="mb-4">Mengoptimalkan pencahayaan alami mengurangi kebutuhan pencahayaan buatan dan beban pendinginan. Strategi seperti clerestory windows, light shelves, dan skylight memberikan cahaya tidak langsung yang ideal.</p>
                
                <h3 class="text-xl font-bold mb-2 mt-6">Kesimpulan</h3>
                <p class="mb-4">Desain rumah tropis modern menghormati kearifan lokal tradisional sambil mengadopsi estetika kontemporer dan teknologi terkini. Hasilnya adalah hunian yang tidak hanya indah dan modern tetapi juga responsif terhadap iklim, hemat energi, dan nyaman untuk ditinggali.</p>',
                'date' => '15 Mei 2025',
                'author' => 'Ir. Anton Wijaya',
                'category' => 'Architecture',
                'tags' => ['tropical', 'modern', 'design']
            ]
        ];
        
        // Check if post exists
        if (!isset($posts[$postId])) {
            // Post not found, redirect to blog index
            redirect('/blog');
            return;
        }
        
        $post = $posts[$postId];
        
        // Related posts (exclude current post)
        $relatedPosts = [];
        foreach ($posts as $p) {
            if ($p['id'] !== $postId && $p['category'] === $post['category']) {
                $relatedPosts[] = $p;
            }
        }
        
        // Get only 3 related posts
        $relatedPosts = array_slice($relatedPosts, 0, 3);
        
        view('pages.blog-detail', [
            'post' => $post,
            'relatedPosts' => $relatedPosts
        ]);
    }
}

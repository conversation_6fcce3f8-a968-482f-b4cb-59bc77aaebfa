<?php
/**
 * Application entry point
 */

// Load configuration
require_once __DIR__ . '/../config/app.php';

// Load helper functions
require_once __DIR__ . '/../app/helpers/functions.php';

// Simple router implementation
function routeRequest()
{
    // Get current URI
    $uri = $_SERVER['REQUEST_URI'] ?? '/';
    
    // Remove query string if present
    if (strpos($uri, '?') !== false) {
        $uri = substr($uri, 0, strpos($uri, '?'));
    }
    
    // Load routes
    $routes = require_once __DIR__ . '/../routes/web.php';
    
    // Check if route exists
    if (array_key_exists($uri, $routes)) {
        [$controller, $method] = $routes[$uri];
        
        // Load controller
        $controllerPath = __DIR__ . "/../app/controllers/{$controller}.php";
        
        if (file_exists($controllerPath)) {
            require_once $controllerPath;
            
            // Create controller instance and call method
            $controllerInstance = new $controller();
            $controllerInstance->$method();
            return;
        }
    }
    
    // Route not found, show 404 page
    header("HTTP/1.0 404 Not Found");
    view('pages.404');
}

// Handle the request
routeRequest();

<?php
/**
 * Web routes for the application
 */

// Define routes as [route => [controller, method]]
return [
    // Main pages
    '/' => ['HomeController', 'index'],
    '/about' => ['HomeController', 'about'],
    '/services' => ['HomeController', 'services'],
    '/portfolio' => ['HomeController', 'portfolio'],
    '/contact' => ['HomeController', 'contact'],
    
    // Contact & Quote forms
    '/contact/submit' => ['ContactController', 'submit'],
    '/quote' => ['QuoteController', 'index'],
    '/quote/submit' => ['QuoteController', 'submit'],
    
    // Thank you page
    '/thank-you' => ['HomeController', 'thankYou'],
    
    // Blog routes
    '/blog' => ['BlogController', 'index'],
    '/blog/([a-z0-9-]+)' => ['BlogController', 'show'],
    '/blog/category/([a-z0-9-]+)' => ['BlogController', 'category'],
    '/blog/tag/([a-z0-9-]+)' => ['BlogController', 'tag'],
    '/blog/search' => ['BlogController', 'search'],
    '/blog/comment' => ['BlogController', 'submitComment'],
    
    // Newsletter subscription
    '/subscribe' => ['HomeController', 'subscribe'],
    
    // 404 Error - this should be the last route
    '/404' => ['ErrorController', 'notFound'],
];

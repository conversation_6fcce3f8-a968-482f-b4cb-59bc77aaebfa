<?php
/**
 * Portfolio page view
 */

// Capture the content for the layout
ob_start();
?>

<!-- Hero Section -->
<section class="relative py-20 bg-cover bg-center" style="background-image: url('<?= asset('images/portfolio-hero.jpg') ?>');">
    <div class="absolute inset-0 bg-primary bg-opacity-80"></div>
    <div class="container mx-auto px-4 z-10 relative">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-serif font-bold text-white mb-4">Portofolio Proyek</h1>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-xl text-white/90 max-w-3xl mx-auto">Eksplorasi karya-karya terbaik Antosa Architect yang telah kami selesaikan untuk berbagai klien.</p>
        </div>
    </div>
</section>

<!-- Portfolio Filter & Grid -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <!-- Filter Buttons -->
        <div class="flex flex-wrap justify-center mb-12">
            <?php foreach ($categories as $category): ?>
            <button class="filter-btn px-6 py-2 mx-2 mb-3 rounded-md <?= $category === 'All' ? 'bg-secondary text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700' ?> transition-colors" data-filter="<?= $category === 'All' ? '*' : $category ?>"><?= $category ?></button>
            <?php endforeach; ?>
        </div>
        
        <!-- Portfolio Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 portfolio-grid">
            <?php foreach ($projects as $project): ?>
            <div class="portfolio-item <?= $project['category'] ?> bg-white rounded-lg shadow-md overflow-hidden group">
                <div class="relative overflow-hidden">
                    <img src="<?= asset('images/' . $project['image']) ?>" alt="<?= $project['title'] ?>" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
                    <div class="absolute inset-0 bg-primary bg-opacity-80 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity p-6">
                        <h3 class="text-xl font-medium text-white mb-2 text-center"><?= $project['title'] ?></h3>
                        <p class="text-white/80 text-center mb-4"><?= $project['description'] ?></p>
                        <a href="<?= $project['link'] ?>" class="bg-secondary hover:bg-white hover:text-primary text-white py-2 px-4 rounded-md transition-colors">Lihat Detail</a>
                    </div>
                </div>
                <div class="p-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-secondary"><?= $project['category'] ?></span>
                        <span class="text-sm text-gray-500"><?= $project['year'] ?></span>
                    </div>
                    <h3 class="text-lg font-medium hover:text-secondary transition-colors">
                        <a href="<?= $project['link'] ?>"><?= $project['title'] ?></a>
                    </h3>
                    <p class="text-gray-600 mt-2 text-sm"><?= $project['location'] ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Project -->
<section class="py-16 md:py-24 bg-gray-100">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-serif font-bold mb-4">Proyek Unggulan</h2>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-gray-700 max-w-3xl mx-auto">Sekilas tentang salah satu proyek kebanggaan kami yang telah mendapatkan pengakuan dalam dunia arsitektur.</p>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="flex flex-col lg:flex-row">
                <div class="lg:w-2/3">
                    <img src="<?= asset('images/featured-project.jpg') ?>" alt="Office Complex Sudirman" class="w-full h-full object-cover">
                </div>
                <div class="lg:w-1/3 p-8">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-secondary">Commercial</span>
                        <span class="text-sm text-gray-500">2023</span>
                    </div>
                    <h3 class="text-2xl font-serif font-bold mb-4">Office Complex Sudirman</h3>
                    <p class="text-gray-600 mb-6">Gedung perkantoran modern dengan fasad kaca dan sistem otomasi pintar yang berlokasi di kawasan bisnis utama Jakarta. Proyek ini menggabungkan estetika kontemporer dengan fitur ramah lingkungan.</p>
                    
                    <div class="space-y-4 mb-8">
                        <div class="flex items-start">
                            <i class="fas fa-map-marker-alt text-secondary mt-1 mr-3"></i>
                            <span>Sudirman, Jakarta Pusat</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-ruler-combined text-secondary mt-1 mr-3"></i>
                            <span>25.000 m²</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-calendar-alt text-secondary mt-1 mr-3"></i>
                            <span>Durasi: 18 bulan</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-trophy text-secondary mt-1 mr-3"></i>
                            <span>Penghargaan Bangunan Hijau 2023</span>
                        </div>
                    </div>
                    
                    <a href="<?= url('/portfolio/office-complex-sudirman') ?>" class="inline-block bg-secondary hover:bg-primary text-white font-medium py-2 px-6 rounded-md transition-colors">Lihat Detail</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Client Logos -->
<section class="py-16 md:py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-serif font-bold mb-4">Klien Kami</h2>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-gray-700 max-w-3xl mx-auto">Beberapa perusahaan dan institusi yang telah mempercayakan proyek mereka kepada Antosa Architect.</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <!-- Client logos would go here, for now using placeholders -->
            <?php for ($i = 1; $i <= 6; $i++): ?>
            <div class="flex items-center justify-center py-6 px-4 bg-gray-50 rounded-lg">
                <div class="text-gray-400 text-center font-medium">Client Logo <?= $i ?></div>
            </div>
            <?php endfor; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 md:py-24 bg-primary">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-serif font-bold text-white mb-4">Siap Untuk Mewujudkan Proyek Anda?</h2>
        <p class="text-lg text-white/80 mb-8 max-w-2xl mx-auto">Jadikan proyek Anda yang berikutnya menjadi bagian dari portofolio kebanggaan kami.</p>
        <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <a href="<?= url('/quote') ?>" class="bg-secondary hover:bg-white hover:text-primary text-white font-medium py-3 px-8 rounded-md transition-colors text-lg">Minta Penawaran</a>
            <a href="<?= url('/contact') ?>" class="bg-transparent hover:bg-white/10 text-white border-2 border-white font-medium py-3 px-8 rounded-md transition-colors text-lg">Hubungi Kami</a>
        </div>
    </div>
</section>

<!-- Portfolio Filtering Script -->
<?php
$extraScripts = <<<EOT
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get all filter buttons and portfolio items
        const filterBtns = document.querySelectorAll('.filter-btn');
        const portfolioItems = document.querySelectorAll('.portfolio-item');
        
        // Add click event to filter buttons
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                filterBtns.forEach(b => b.classList.remove('bg-secondary', 'text-white'));
                filterBtns.forEach(b => b.classList.add('bg-gray-100', 'text-gray-700'));
                
                // Add active class to clicked button
                this.classList.remove('bg-gray-100', 'text-gray-700');
                this.classList.add('bg-secondary', 'text-white');
                
                // Get filter value
                const filterValue = this.getAttribute('data-filter');
                
                // Filter portfolio items
                portfolioItems.forEach(item => {
                    if (filterValue === '*' || item.classList.contains(filterValue)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
EOT;
?>

<?php
// Set the content variable for the layout
$content = ob_get_clean();

// Set page-specific variables
$pageTitle = 'Portofolio';
$pageDescription = 'Eksplorasi proyek-proyek arsitektur dan konstruksi terbaik yang telah diselesaikan oleh Antosa Architect';

// Load the layout
require_once VIEWS_PATH . '/layouts/main.php';
?>

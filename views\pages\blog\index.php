<?php
/**
 * Blog index page view
 */

// Capture the content for the layout
ob_start();
?>

<!-- Hero Section -->
<section class="relative py-20 bg-cover bg-center" style="background-image: url('<?= asset('images/blog-hero.jpg') ?>');">
    <div class="absolute inset-0 bg-primary bg-opacity-80"></div>
    <div class="container mx-auto px-4 z-10 relative">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-serif font-bold text-white mb-4">Blog</h1>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-xl text-white/90 max-w-3xl mx-auto">W<PERSON><PERSON>, tips, dan inspirasi tentang arsitektur, desain, konstruksi, dan tren terkini.</p>
        </div>
    </div>
</section>

<!-- Blog Posts -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row">
            <!-- Main Content -->
            <div class="lg:w-2/3 lg:pr-12">
                <!-- Featured Post -->
                <?php if (!empty($featuredPost)): ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-12">
                    <div class="relative">
                        <img src="<?= asset('images/' . $featuredPost['image']) ?>" alt="<?= $featuredPost['title'] ?>" class="w-full h-80 object-cover">
                        <div class="absolute top-0 left-0 bg-secondary text-white py-2 px-6 uppercase text-sm font-bold">
                            Featured
                        </div>
                    </div>
                    <div class="p-8">
                        <div class="flex flex-wrap items-center text-sm text-gray-500 mb-4">
                            <span class="mr-6">
                                <i class="far fa-calendar-alt mr-2"></i> <?= date('d M Y', strtotime($featuredPost['date'])) ?>
                            </span>
                            <span class="mr-6">
                                <i class="far fa-user mr-2"></i> <?= $featuredPost['author'] ?>
                            </span>
                            <span>
                                <i class="far fa-folder mr-2"></i> <?= $featuredPost['category'] ?>
                            </span>
                        </div>
                        <h2 class="text-2xl font-serif font-bold mb-4">
                            <a href="<?= url('/blog/' . $featuredPost['slug']) ?>" class="hover:text-secondary transition-colors">
                                <?= $featuredPost['title'] ?>
                            </a>
                        </h2>
                        <p class="text-gray-600 mb-6"><?= $featuredPost['excerpt'] ?></p>
                        <a href="<?= url('/blog/' . $featuredPost['slug']) ?>" class="inline-block bg-secondary hover:bg-primary text-white font-medium py-2 px-6 rounded-md transition-colors">
                            Baca Selengkapnya
                        </a>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Blog Post Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <?php foreach ($blogPosts as $post): ?>
                    <div class="bg-white rounded-lg shadow-md overflow-hidden group">
                        <div class="relative overflow-hidden h-48">
                            <img src="<?= asset('images/' . $post['image']) ?>" alt="<?= $post['title'] ?>" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        </div>
                        <div class="p-6">
                            <div class="flex flex-wrap items-center text-xs text-gray-500 mb-3">
                                <span class="mr-4">
                                    <i class="far fa-calendar-alt mr-1"></i> <?= date('d M Y', strtotime($post['date'])) ?>
                                </span>
                                <span>
                                    <i class="far fa-folder mr-1"></i> <?= $post['category'] ?>
                                </span>
                            </div>
                            <h3 class="text-xl font-medium mb-3">
                                <a href="<?= url('/blog/' . $post['slug']) ?>" class="hover:text-secondary transition-colors">
                                    <?= $post['title'] ?>
                                </a>
                            </h3>
                            <p class="text-gray-600 mb-4 text-sm"><?= $post['excerpt'] ?></p>
                            <a href="<?= url('/blog/' . $post['slug']) ?>" class="inline-block text-secondary hover:text-primary font-medium transition-colors text-sm">
                                Baca Selengkapnya <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="mt-12 flex justify-center">
                    <div class="inline-flex rounded-md shadow-sm">
                        <?php if ($currentPage > 1): ?>
                        <a href="<?= url('/blog?page=' . ($currentPage - 1)) ?>" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <?php else: ?>
                        <span class="px-4 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded-l-md">
                            <i class="fas fa-chevron-left"></i>
                        </span>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if ($i == $currentPage): ?>
                        <span class="px-4 py-2 text-sm font-medium text-white bg-secondary border border-secondary">
                            <?= $i ?>
                        </span>
                        <?php else: ?>
                        <a href="<?= url('/blog?page=' . $i) ?>" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50">
                            <?= $i ?>
                        </a>
                        <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php if ($currentPage < $totalPages): ?>
                        <a href="<?= url('/blog?page=' . ($currentPage + 1)) ?>" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <?php else: ?>
                        <span class="px-4 py-2 text-sm font-medium text-gray-300 bg-white border border-gray-300 rounded-r-md">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:w-1/3 mt-12 lg:mt-0">
                <!-- Search -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Pencarian</h3>
                    <form action="<?= url('/blog/search') ?>" method="get">
                        <div class="flex">
                            <input type="text" name="q" placeholder="Cari artikel..." class="flex-grow px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                            <button type="submit" class="bg-secondary hover:bg-primary text-white px-4 py-2 rounded-r-md transition-colors">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Categories -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Kategori</h3>
                    <ul class="space-y-3">
                        <?php foreach ($categories as $category => $count): ?>
                        <li>
                            <a href="<?= url('/blog/category/' . strtolower(str_replace(' ', '-', $category))) ?>" class="flex justify-between items-center hover:text-secondary transition-colors">
                                <span><?= $category ?></span>
                                <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full"><?= $count ?></span>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Recent Posts -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Artikel Terbaru</h3>
                    <div class="space-y-4">
                        <?php foreach ($recentPosts as $post): ?>
                        <div class="flex space-x-4">
                            <div class="flex-shrink-0">
                                <img src="<?= asset('images/' . $post['image']) ?>" alt="<?= $post['title'] ?>" class="w-20 h-20 object-cover rounded">
                            </div>
                            <div>
                                <h4 class="font-medium text-sm mb-1">
                                    <a href="<?= url('/blog/' . $post['slug']) ?>" class="hover:text-secondary transition-colors">
                                        <?= $post['title'] ?>
                                    </a>
                                </h4>
                                <p class="text-gray-500 text-xs"><?= date('d M Y', strtotime($post['date'])) ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Tags -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($tags as $tag): ?>
                        <a href="<?= url('/blog/tag/' . strtolower(str_replace(' ', '-', $tag))) ?>" class="bg-gray-100 hover:bg-secondary hover:text-white text-gray-700 px-3 py-1 rounded-full text-sm transition-colors">
                            <?= $tag ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Subscribe -->
                <div class="bg-primary rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-medium text-white mb-4">Berlangganan Newsletter</h3>
                    <p class="text-white/80 mb-4 text-sm">Dapatkan artikel terbaru dan tips arsitektur langsung ke inbox Anda.</p>
                    <form action="<?= url('/subscribe') ?>" method="post">
                        <input type="email" name="email" placeholder="Email Anda" required class="w-full px-4 py-2 mb-3 border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        <button type="submit" class="w-full bg-secondary hover:bg-white hover:text-primary text-white font-medium py-2 px-4 rounded-md transition-colors">
                            Berlangganan
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Set the content variable for the layout
$content = ob_get_clean();

// Set page-specific variables
$pageTitle = 'Blog';
$pageDescription = 'Baca artikel terbaru tentang arsitektur, desain, konstruksi, dan tren terkini dari tim ahli Antosa Architect';

// Load the layout
require_once VIEWS_PATH . '/layouts/main.php';
?>

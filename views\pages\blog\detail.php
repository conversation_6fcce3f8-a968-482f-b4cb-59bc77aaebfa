<?php
/**
 * Blog detail page view
 */

// Capture the content for the layout
ob_start();
?>

<!-- Blog Post Content -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row">
            <!-- Main Content -->
            <div class="lg:w-2/3 lg:pr-12">
                <!-- Breadcrumbs -->
                <div class="flex items-center text-sm text-gray-500 mb-8">
                    <a href="<?= url('/') ?>" class="hover:text-secondary transition-colors">Home</a>
                    <span class="mx-2">/</span>
                    <a href="<?= url('/blog') ?>" class="hover:text-secondary transition-colors">Blog</a>
                    <span class="mx-2">/</span>
                    <span class="text-gray-700"><?= $post['title'] ?></span>
                </div>
                
                <!-- Post Header -->
                <div class="mb-8">
                    <h1 class="text-3xl md:text-4xl font-serif font-bold mb-6"><?= $post['title'] ?></h1>
                    <div class="flex flex-wrap items-center text-sm text-gray-500 mb-6">
                        <span class="mr-6">
                            <i class="far fa-calendar-alt mr-2"></i> <?= date('d M Y', strtotime($post['date'])) ?>
                        </span>
                        <span class="mr-6">
                            <i class="far fa-user mr-2"></i> <?= $post['author'] ?>
                        </span>
                        <span class="mr-6">
                            <i class="far fa-folder mr-2"></i> <?= $post['category'] ?>
                        </span>
                        <span>
                            <i class="far fa-comment mr-2"></i> <?= count($post['comments']) ?> Comments
                        </span>
                    </div>
                </div>
                
                <!-- Featured Image -->
                <div class="mb-8">
                    <img src="<?= asset('images/' . $post['image']) ?>" alt="<?= $post['title'] ?>" class="w-full h-auto rounded-lg shadow-md">
                </div>
                
                <!-- Post Content -->
                <div class="prose max-w-none mb-12">
                    <?= $post['content'] ?>
                </div>
                
                <!-- Tags -->
                <?php if (!empty($post['tags'])): ?>
                <div class="mb-12">
                    <div class="flex items-center flex-wrap">
                        <span class="mr-3 font-medium">Tags:</span>
                        <?php foreach ($post['tags'] as $tag): ?>
                        <a href="<?= url('/blog/tag/' . strtolower(str_replace(' ', '-', $tag))) ?>" class="bg-gray-100 hover:bg-secondary hover:text-white text-gray-700 px-3 py-1 rounded-full text-sm transition-colors mr-2 mb-2">
                            <?= $tag ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Share Buttons -->
                <div class="mb-12">
                    <div class="flex items-center flex-wrap">
                        <span class="mr-3 font-medium">Share:</span>
                        <a href="https://facebook.com/sharer/sharer.php?u=<?= urlencode(url('/blog/' . $post['slug'])) ?>" target="_blank" class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-2 hover:opacity-80 transition-opacity">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?= urlencode(url('/blog/' . $post['slug'])) ?>&text=<?= urlencode($post['title']) ?>" target="_blank" class="w-8 h-8 bg-blue-400 text-white rounded-full flex items-center justify-center mr-2 hover:opacity-80 transition-opacity">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://www.linkedin.com/shareArticle?mini=true&url=<?= urlencode(url('/blog/' . $post['slug'])) ?>&title=<?= urlencode($post['title']) ?>" target="_blank" class="w-8 h-8 bg-blue-700 text-white rounded-full flex items-center justify-center mr-2 hover:opacity-80 transition-opacity">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="https://wa.me/?text=<?= urlencode($post['title'] . ' ' . url('/blog/' . $post['slug'])) ?>" target="_blank" class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center mr-2 hover:opacity-80 transition-opacity">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Author Box -->
                <div class="bg-gray-50 rounded-lg p-6 mb-12 flex flex-col md:flex-row items-center md:items-start">
                    <div class="w-24 h-24 rounded-full overflow-hidden mb-4 md:mb-0 md:mr-6 flex-shrink-0">
                        <img src="<?= asset('images/authors/' . $author['image']) ?>" alt="<?= $author['name'] ?>" class="w-full h-full object-cover">
                    </div>
                    <div>
                        <h3 class="text-xl font-medium mb-2"><?= $author['name'] ?></h3>
                        <p class="text-gray-600 mb-4"><?= $author['bio'] ?></p>
                        <div class="flex space-x-3">
                            <?php if (isset($author['social']['facebook'])): ?>
                            <a href="<?= $author['social']['facebook'] ?>" class="text-gray-500 hover:text-secondary transition-colors">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <?php endif; ?>
                            
                            <?php if (isset($author['social']['twitter'])): ?>
                            <a href="<?= $author['social']['twitter'] ?>" class="text-gray-500 hover:text-secondary transition-colors">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <?php endif; ?>
                            
                            <?php if (isset($author['social']['linkedin'])): ?>
                            <a href="<?= $author['social']['linkedin'] ?>" class="text-gray-500 hover:text-secondary transition-colors">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Post Navigation -->
                <div class="flex flex-col sm:flex-row justify-between border-t border-b border-gray-200 py-6 mb-12">
                    <?php if (isset($prevPost)): ?>
                    <div class="mb-4 sm:mb-0">
                        <span class="block text-sm text-gray-500 mb-1">Previous Post</span>
                        <a href="<?= url('/blog/' . $prevPost['slug']) ?>" class="font-medium hover:text-secondary transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> <?= $prevPost['title'] ?>
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($nextPost)): ?>
                    <div class="text-right">
                        <span class="block text-sm text-gray-500 mb-1">Next Post</span>
                        <a href="<?= url('/blog/' . $nextPost['slug']) ?>" class="font-medium hover:text-secondary transition-colors">
                            <?= $nextPost['title'] ?> <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Related Posts -->
                <div class="mb-12">
                    <h3 class="text-2xl font-serif font-bold mb-6">Related Posts</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <?php foreach ($relatedPosts as $relatedPost): ?>
                        <div class="bg-white rounded-lg shadow-md overflow-hidden group">
                            <div class="relative overflow-hidden h-48">
                                <img src="<?= asset('images/' . $relatedPost['image']) ?>" alt="<?= $relatedPost['title'] ?>" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                            </div>
                            <div class="p-6">
                                <div class="flex flex-wrap items-center text-xs text-gray-500 mb-3">
                                    <span class="mr-4">
                                        <i class="far fa-calendar-alt mr-1"></i> <?= date('d M Y', strtotime($relatedPost['date'])) ?>
                                    </span>
                                    <span>
                                        <i class="far fa-folder mr-1"></i> <?= $relatedPost['category'] ?>
                                    </span>
                                </div>
                                <h3 class="text-xl font-medium mb-3">
                                    <a href="<?= url('/blog/' . $relatedPost['slug']) ?>" class="hover:text-secondary transition-colors">
                                        <?= $relatedPost['title'] ?>
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-4 text-sm"><?= $relatedPost['excerpt'] ?></p>
                                <a href="<?= url('/blog/' . $relatedPost['slug']) ?>" class="inline-block text-secondary hover:text-primary font-medium transition-colors text-sm">
                                    Baca Selengkapnya <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Comments Section -->
                <div>
                    <h3 class="text-2xl font-serif font-bold mb-6">Comments (<?= count($post['comments']) ?>)</h3>
                    
                    <!-- Comments List -->
                    <div class="space-y-8 mb-12">
                        <?php foreach ($post['comments'] as $comment): ?>
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex items-start">
                                <div class="w-12 h-12 rounded-full overflow-hidden mr-4 flex-shrink-0">
                                    <img src="<?= asset('images/avatar.jpg') ?>" alt="<?= $comment['name'] ?>" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-grow">
                                    <div class="flex flex-wrap justify-between mb-2">
                                        <h4 class="font-medium"><?= $comment['name'] ?></h4>
                                        <span class="text-sm text-gray-500"><?= date('d M Y', strtotime($comment['date'])) ?></span>
                                    </div>
                                    <p class="text-gray-600"><?= $comment['content'] ?></p>
                                    
                                    <!-- Reply link (functionality would be added with JavaScript) -->
                                    <button class="text-secondary hover:text-primary mt-2 text-sm font-medium transition-colors reply-btn">
                                        Reply
                                    </button>
                                    
                                    <!-- Nested Replies (if any) -->
                                    <?php if (!empty($comment['replies'])): ?>
                                    <div class="mt-6 space-y-6">
                                        <?php foreach ($comment['replies'] as $reply): ?>
                                        <div class="flex items-start">
                                            <div class="w-10 h-10 rounded-full overflow-hidden mr-3 flex-shrink-0">
                                                <img src="<?= asset('images/avatar.jpg') ?>" alt="<?= $reply['name'] ?>" class="w-full h-full object-cover">
                                            </div>
                                            <div>
                                                <div class="flex flex-wrap justify-between mb-2">
                                                    <h4 class="font-medium"><?= $reply['name'] ?></h4>
                                                    <span class="text-sm text-gray-500"><?= date('d M Y', strtotime($reply['date'])) ?></span>
                                                </div>
                                                <p class="text-gray-600"><?= $reply['content'] ?></p>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Comment Form -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-xl font-medium mb-6">Leave a Comment</h3>
                        <form action="<?= url('/blog/comment') ?>" method="post">
                            <input type="hidden" name="post_id" value="<?= $post['id'] ?>">
                            <input type="hidden" name="post_slug" value="<?= $post['slug'] ?>">
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <!-- Name -->
                                <div>
                                    <label for="name" class="block text-gray-700 font-medium mb-2">Nama <span class="text-red-500">*</span></label>
                                    <input type="text" id="name" name="name" required class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                </div>
                                
                                <!-- Email -->
                                <div>
                                    <label for="email" class="block text-gray-700 font-medium mb-2">Email <span class="text-red-500">*</span></label>
                                    <input type="email" id="email" name="email" required class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                </div>
                            </div>
                            
                            <!-- Comment -->
                            <div class="mb-6">
                                <label for="comment" class="block text-gray-700 font-medium mb-2">Komentar <span class="text-red-500">*</span></label>
                                <textarea id="comment" name="comment" rows="5" required class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"></textarea>
                            </div>
                            
                            <!-- Terms -->
                            <div class="mb-6">
                                <div class="flex items-start">
                                    <input type="checkbox" id="terms" name="terms" required class="h-5 w-5 mt-1 text-secondary focus:ring-secondary">
                                    <label for="terms" class="ml-2 text-gray-700">
                                        Saya menyetujui bahwa data saya akan disimpan sesuai dengan <a href="#" class="text-secondary hover:underline">Kebijakan Privasi</a> <span class="text-red-500">*</span>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <button type="submit" class="inline-block bg-secondary hover:bg-primary text-white font-medium py-3 px-8 rounded-md transition-colors">
                                Submit Comment
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:w-1/3 mt-12 lg:mt-0">
                <!-- Search -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Pencarian</h3>
                    <form action="<?= url('/blog/search') ?>" method="get">
                        <div class="flex">
                            <input type="text" name="q" placeholder="Cari artikel..." class="flex-grow px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                            <button type="submit" class="bg-secondary hover:bg-primary text-white px-4 py-2 rounded-r-md transition-colors">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Categories -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Kategori</h3>
                    <ul class="space-y-3">
                        <?php foreach ($categories as $category => $count): ?>
                        <li>
                            <a href="<?= url('/blog/category/' . strtolower(str_replace(' ', '-', $category))) ?>" class="flex justify-between items-center hover:text-secondary transition-colors">
                                <span><?= $category ?></span>
                                <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full"><?= $count ?></span>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Recent Posts -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Artikel Terbaru</h3>
                    <div class="space-y-4">
                        <?php foreach ($recentPosts as $recentPost): ?>
                        <div class="flex space-x-4">
                            <div class="flex-shrink-0">
                                <img src="<?= asset('images/' . $recentPost['image']) ?>" alt="<?= $recentPost['title'] ?>" class="w-20 h-20 object-cover rounded">
                            </div>
                            <div>
                                <h4 class="font-medium text-sm mb-1">
                                    <a href="<?= url('/blog/' . $recentPost['slug']) ?>" class="hover:text-secondary transition-colors">
                                        <?= $recentPost['title'] ?>
                                    </a>
                                </h4>
                                <p class="text-gray-500 text-xs"><?= date('d M Y', strtotime($recentPost['date'])) ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Tags -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <h3 class="text-xl font-medium mb-4">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($tags as $tag): ?>
                        <a href="<?= url('/blog/tag/' . strtolower(str_replace(' ', '-', $tag))) ?>" class="bg-gray-100 hover:bg-secondary hover:text-white text-gray-700 px-3 py-1 rounded-full text-sm transition-colors">
                            <?= $tag ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Subscribe -->
                <div class="bg-primary rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-medium text-white mb-4">Berlangganan Newsletter</h3>
                    <p class="text-white/80 mb-4 text-sm">Dapatkan artikel terbaru dan tips arsitektur langsung ke inbox Anda.</p>
                    <form action="<?= url('/subscribe') ?>" method="post">
                        <input type="email" name="email" placeholder="Email Anda" required class="w-full px-4 py-2 mb-3 border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        <button type="submit" class="w-full bg-secondary hover:bg-white hover:text-primary text-white font-medium py-2 px-4 rounded-md transition-colors">
                            Berlangganan
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Comment Reply JS -->
<?php
$extraScripts = <<<EOT
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Reply functionality would be implemented here
        const replyBtns = document.querySelectorAll('.reply-btn');
        
        replyBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Scroll to comment form
                const commentForm = document.querySelector('form[action*="/blog/comment"]');
                const nameInput = document.getElementById('name');
                
                commentForm.scrollIntoView({ behavior: 'smooth' });
                
                // Focus on name input after scroll
                setTimeout(() => {
                    nameInput.focus();
                }, 500);
            });
        });
    });
</script>
EOT;
?>

<?php
// Set the content variable for the layout
$content = ob_get_clean();

// Set page-specific variables
$pageTitle = $post['title'];
$pageDescription = $post['excerpt'];

// Load the layout
require_once VIEWS_PATH . '/layouts/main.php';
?>

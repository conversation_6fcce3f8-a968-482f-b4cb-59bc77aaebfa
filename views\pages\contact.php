<?php
/**
 * Contact page view
 */

// Capture the content for the layout
ob_start();
?>

<!-- Hero Section -->
<section class="relative py-20 bg-cover bg-center" style="background-image: url('<?= asset('images/contact-hero.jpg') ?>');">
    <div class="absolute inset-0 bg-primary bg-opacity-80"></div>
    <div class="container mx-auto px-4 z-10 relative">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-serif font-bold text-white mb-4">Hubung<PERSON> Kami</h1>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-xl text-white/90 max-w-3xl mx-auto"><PERSON>an ragu untuk menghubungi kami dengan pertanyaan, permintaan, atau konsultasi tentang proyek Anda.</p>
        </div>
    </div>
</section>

<!-- Contact Info & Form Section -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row">
            <!-- Contact Information -->
            <div class="lg:w-1/3 mb-12 lg:mb-0 lg:pr-12">
                <h2 class="text-3xl font-serif font-bold mb-6">Informasi Kontak</h2>
                <div class="w-20 h-1 bg-secondary mb-8"></div>
                
                <div class="space-y-8">
                    <!-- Office Address -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-map-marker-alt text-secondary"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium mb-2">Kantor Pusat</h4>
                            <p class="text-gray-600"><?= COMPANY_ADDRESS ?></p>
                        </div>
                    </div>
                    
                    <!-- Phone -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-phone-alt text-secondary"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium mb-2">Telepon</h4>
                            <p class="text-gray-600"><?= COMPANY_PHONE ?></p>
                        </div>
                    </div>
                    
                    <!-- Email -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-envelope text-secondary"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium mb-2">Email</h4>
                            <p class="text-gray-600"><?= COMPANY_EMAIL ?></p>
                        </div>
                    </div>
                    
                    <!-- Working Hours -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-secondary/10 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-secondary"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium mb-2">Jam Kerja</h4>
                            <p class="text-gray-600">Senin - Jumat: 08.00 - 17.00</p>
                            <p class="text-gray-600">Sabtu: 09.00 - 13.00</p>
                            <p class="text-gray-600">Minggu: Tutup</p>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="mt-12">
                    <h4 class="text-lg font-medium mb-4">Ikuti Kami</h4>
                    <div class="flex space-x-4">
                        <?php if (COMPANY_INSTAGRAM): ?>
                        <a href="<?= COMPANY_INSTAGRAM ?>" class="w-10 h-10 bg-secondary/10 rounded-full flex items-center justify-center hover:bg-secondary hover:text-white transition-colors text-secondary">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (COMPANY_FACEBOOK): ?>
                        <a href="<?= COMPANY_FACEBOOK ?>" class="w-10 h-10 bg-secondary/10 rounded-full flex items-center justify-center hover:bg-secondary hover:text-white transition-colors text-secondary">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (COMPANY_TWITTER): ?>
                        <a href="<?= COMPANY_TWITTER ?>" class="w-10 h-10 bg-secondary/10 rounded-full flex items-center justify-center hover:bg-secondary hover:text-white transition-colors text-secondary">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (COMPANY_LINKEDIN): ?>
                        <a href="<?= COMPANY_LINKEDIN ?>" class="w-10 h-10 bg-secondary/10 rounded-full flex items-center justify-center hover:bg-secondary hover:text-white transition-colors text-secondary">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="lg:w-2/3 bg-gray-50 rounded-lg p-8">
                <h2 class="text-3xl font-serif font-bold mb-6">Kirim Pesan</h2>
                <div class="w-20 h-1 bg-secondary mb-8"></div>
                
                <?php if (isset($success)): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-8">
                        <p>Terima kasih telah menghubungi kami. Pesan Anda telah berhasil dikirim. Kami akan segera menghubungi Anda kembali.</p>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-8">
                        <ul class="list-disc list-inside">
                            <?php foreach ($errors as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form action="<?= url('/contact/submit') ?>" method="post" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-gray-700 font-medium mb-2">Nama Lengkap <span class="text-red-500">*</span></label>
                            <input type="text" id="name" name="name" value="<?= isset($old['name']) ? $old['name'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['name']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        
                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-gray-700 font-medium mb-2">Email <span class="text-red-500">*</span></label>
                            <input type="email" id="email" name="email" value="<?= isset($old['email']) ? $old['email'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['email']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-gray-700 font-medium mb-2">Telepon</label>
                            <input type="tel" id="phone" name="phone" value="<?= isset($old['phone']) ? $old['phone'] : '' ?>" class="w-full px-4 py-3 border <?= isset($errors['phone']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        
                        <!-- Subject -->
                        <div>
                            <label for="subject" class="block text-gray-700 font-medium mb-2">Subjek <span class="text-red-500">*</span></label>
                            <input type="text" id="subject" name="subject" value="<?= isset($old['subject']) ? $old['subject'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['subject']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                    </div>
                    
                    <!-- Message -->
                    <div>
                        <label for="message" class="block text-gray-700 font-medium mb-2">Pesan <span class="text-red-500">*</span></label>
                        <textarea id="message" name="message" rows="6" required class="w-full px-4 py-3 border <?= isset($errors['message']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"><?= isset($old['message']) ? $old['message'] : '' ?></textarea>
                    </div>
                    
                    <!-- Submit Button -->
                    <div>
                        <button type="submit" class="inline-block bg-secondary hover:bg-primary text-white font-medium py-3 px-8 rounded-md transition-colors text-lg">Kirim Pesan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-16 md:py-24 bg-gray-100">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-serif font-bold mb-4">Lokasi Kami</h2>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-gray-700 max-w-3xl mx-auto">Kunjungi kantor kami untuk diskusi langsung tentang proyek Anda.</p>
        </div>
        
        <div class="bg-white rounded-lg shadow-md overflow-hidden h-96">
            <!-- Placeholder for the map, would be replaced with actual Google Maps or other map implementation -->
            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                <div class="text-gray-600 text-center">
                    <i class="fas fa-map-marked-alt text-6xl mb-4 text-secondary"></i>
                    <p>Google Maps akan ditampilkan di sini</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-serif font-bold mb-4">Pertanyaan Umum</h2>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-gray-700 max-w-3xl mx-auto">Berikut adalah beberapa pertanyaan yang sering diajukan oleh klien kami.</p>
        </div>
        
        <div class="max-w-3xl mx-auto space-y-6">
            <?php foreach ($faqs as $index => $faq): ?>
            <div class="bg-gray-50 rounded-lg">
                <button class="faq-btn w-full flex justify-between items-center px-6 py-4 focus:outline-none" data-index="<?= $index ?>">
                    <h3 class="text-lg font-medium text-left"><?= $faq['question'] ?></h3>
                    <i class="fas fa-chevron-down text-secondary transition-transform"></i>
                </button>
                <div class="faq-answer px-6 pb-4 hidden">
                    <p class="text-gray-600"><?= $faq['answer'] ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- FAQ Toggle Script -->
<?php
$extraScripts = <<<EOT
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const faqBtns = document.querySelectorAll('.faq-btn');
        const faqAnswers = document.querySelectorAll('.faq-answer');
        
        faqBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const index = this.getAttribute('data-index');
                const answer = faqAnswers[index];
                const icon = this.querySelector('i');
                
                // Toggle answer visibility
                if (answer.classList.contains('hidden')) {
                    answer.classList.remove('hidden');
                    icon.classList.add('transform', 'rotate-180');
                } else {
                    answer.classList.add('hidden');
                    icon.classList.remove('transform', 'rotate-180');
                }
            });
        });
    });
</script>
EOT;
?>

<?php
// Set the content variable for the layout
$content = ob_get_clean();

// Set page-specific variables
$pageTitle = 'Hubungi Kami';
$pageDescription = 'Hubungi Antosa Architect untuk konsultasi, pertanyaan, atau kebutuhan proyek arsitektur dan konstruksi Anda';

// Load the layout
require_once VIEWS_PATH . '/layouts/main.php';
?>

<?php
/**
 * Home Controller
 * Handles main pages of the website
 */
class HomeController
{
    /**
     * Display the home page
     */
    public function index()
    {
        // Data for the hero section
        $heroData = [
            'title' => 'Mewujudkan Desain Impian Anda',
            'subtitle' => 'Jasa arsitektur dan konstruksi profesional dengan pendekatan yang inovatif',
            'ctaText' => 'Konsultasi Gratis',
            'ctaLink' => url('/contact'),
            'portfolioLink' => url('/portfolio'),
        ];
        
        // Data for services section
        $services = [
            [
                'icon' => 'building',
                'title' => 'Desain Arsitektur',
                'description' => 'Desain bangunan yang fungsional dan estetis sesuai kebutuhan Anda'
            ],
            [
                'icon' => 'hard-hat',
                'title' => 'Konstruksi Bangunan',
                'description' => 'Pelaksanaan konstruksi dengan kualitas terbaik dan tepat waktu'
            ],
            [
                'icon' => 'tools',
                'title' => 'Renovasi dan <PERSON>',
                'description' => 'Memperbaharui atau mengembalikan bangunan ke kondisi terbaiknya'
            ],
            [
                'icon' => 'couch',
                'title' => 'Desain Interior',
                'description' => 'Menciptakan ruang dalam yang nyaman dan sesuai dengan kepribadian Anda'
            ]
        ];
        
        // Featured projects
        $featuredProjects = [
            [
                'image' => 'project-1.jpg',
                'title' => 'Rumah Minimalis Kebayoran',
                'category' => 'Residential',
                'year' => '2024',
                'link' => url('/portfolio/rumah-minimalis-kebayoran')
            ],
            [
                'image' => 'project-2.jpg',
                'title' => 'Office Complex Sudirman',
                'category' => 'Commercial',
                'year' => '2023',
                'link' => url('/portfolio/office-complex-sudirman')
            ],
            [
                'image' => 'project-3.jpg',
                'title' => 'Villa Puncak',
                'category' => 'Residential',
                'year' => '2023',
                'link' => url('/portfolio/villa-puncak')
            ]
        ];
        
        // Testimonials
        $testimonials = [
            [
                'content' => 'Antosa Architect mampu mewujudkan rumah impian kami dengan desain yang luar biasa dan pengerjaan yang rapi.',
                'name' => 'Budi Santoso',
                'position' => 'Pemilik Rumah di Serpong',
                'rating' => 5
            ],
            [
                'content' => 'Proses renovasi kantor kami berjalan sangat lancar berkat tim Antosa Architect. Hasil akhirnya sangat memuaskan.',
                'name' => 'Diana Putri',
                'position' => 'CEO PT Maju Bersama',
                'rating' => 5
            ],
            [
                'content' => 'Desain yang modern dan fungsional membuat villa kami menjadi sangat nyaman. Terima kasih Antosa Architect!',
                'name' => 'Hendri Wijaya',
                'position' => 'Pengusaha',
                'rating' => 4
            ]
        ];
        
        // Recent blog posts
        $recentPosts = [
            [
                'image' => 'blog-1.jpg',
                'title' => 'Tren Desain Arsitektur 2025',
                'excerpt' => 'Mengenal lebih dekat tren desain arsitektur yang akan populer di tahun 2025',
                'date' => '1 Juni 2025',
                'link' => url('/blog/post?id=1')
            ],
            [
                'image' => 'blog-2.jpg',
                'title' => 'Tips Merenovasi Rumah dengan Budget Terbatas',
                'excerpt' => 'Panduan praktis untuk merenovasi rumah tanpa menghabiskan banyak biaya',
                'date' => '25 Mei 2025',
                'link' => url('/blog/post?id=2')
            ],
            [
                'image' => 'blog-3.jpg',
                'title' => 'Desain Rumah Tropis Modern',
                'excerpt' => 'Mengadaptasi gaya tropis ke dalam desain rumah modern yang nyaman',
                'date' => '15 Mei 2025',
                'link' => url('/blog/post?id=3')
            ]
        ];
        
        // Load the view with all data
        view('pages.home', [
            'heroData' => $heroData,
            'services' => $services,
            'featuredProjects' => $featuredProjects,
            'testimonials' => $testimonials,
            'recentPosts' => $recentPosts
        ]);
    }
    
    /**
     * Display the about page
     */
    public function about()
    {
        $teamMembers = [
            [
                'image' => 'team-1.jpg',
                'name' => 'Ir. Anton Wijaya',
                'position' => 'Principal Architect',
                'bio' => 'Berpengalaman lebih dari 15 tahun dalam dunia arsitektur, lulusan Institut Teknologi Bandung'
            ],
            [
                'image' => 'team-2.jpg',
                'name' => 'Sari Utami, M.Arch',
                'position' => 'Senior Architect',
                'bio' => 'Spesialis desain sustainable architecture dengan pengalaman internasional'
            ],
            [
                'image' => 'team-3.jpg',
                'name' => 'Budi Santoso, S.T.',
                'position' => 'Construction Manager',
                'bio' => 'Ahli manajemen konstruksi dengan fokus pada kualitas dan efisiensi pelaksanaan'
            ],
            [
                'image' => 'team-4.jpg',
                'name' => 'Maya Indriani, S.Ds.',
                'position' => 'Interior Designer',
                'bio' => 'Desainer interior kreatif dengan keahlian dalam menciptakan ruang yang fungsional dan estetis'
            ]
        ];
        
        $certifications = [
            'Ikatan Arsitek Indonesia (IAI)',
            'Persatuan Insinyur Indonesia (PII)',
            'Lembaga Pengembangan Jasa Konstruksi (LPJK)',
            'Green Building Council Indonesia (GBCI)'
        ];
        
        $milestones = [
            ['year' => '2010', 'event' => 'Pendirian Antosa Architect'],
            ['year' => '2015', 'event' => 'Penyelesaian 50 proyek residensial'],
            ['year' => '2018', 'event' => 'Ekspansi ke proyek komersial skala besar'],
            ['year' => '2020', 'event' => 'Penghargaan Arsitek Terbaik dari IAI'],
            ['year' => '2023', 'event' => 'Pembukaan kantor cabang di Surabaya'],
            ['year' => '2025', 'event' => 'Mencapai 200+ proyek yang sukses diselesaikan']
        ];
        
        view('pages.about', [
            'teamMembers' => $teamMembers,
            'certifications' => $certifications,
            'milestones' => $milestones
        ]);
    }
    
    /**
     * Display the services page
     */
    public function services()
    {
        $services = [
            [
                'icon' => 'building',
                'title' => 'Desain Arsitektur',
                'description' => 'Layanan desain arsitektur mencakup perencanaan bangunan yang memadukan estetika, fungsi, dan efisiensi. Kami memulai dari konsep, dilanjutkan dengan pengembangan desain, dan akhirnya menghasilkan gambar kerja yang detail.',
                'features' => [
                    'Konsep dan Skema Desain',
                    'Gambar Presentasi 2D dan 3D',
                    'Gambar Kerja Detail',
                    'Rencana Anggaran Biaya (RAB)',
                    'Pendampingan Izin Mendirikan Bangunan (IMB)'
                ]
            ],
            [
                'icon' => 'hard-hat',
                'title' => 'Konstruksi Bangunan',
                'description' => 'Layanan konstruksi mencakup pelaksanaan pembangunan dengan manajemen proyek yang profesional. Kami menangani seluruh proses mulai dari persiapan lahan hingga finishing dan serah terima bangunan.',
                'features' => [
                    'Manajemen Konstruksi',
                    'Pengawasan Berkala dan Rutin',
                    'Pengendalian Kualitas',
                    'Manajemen Pengadaan Material',
                    'Koordinasi dengan Subkontraktor'
                ]
            ],
            [
                'icon' => 'tools',
                'title' => 'Renovasi dan Restorasi',
                'description' => 'Layanan renovasi dan restorasi untuk memperbaharui bangunan yang sudah ada atau mengembalikan bangunan bersejarah ke kondisi aslinya dengan tetap memperhatikan kaidah arsitektur dan struktural.',
                'features' => [
                    'Asesmen Kondisi Bangunan',
                    'Desain Renovasi',
                    'Perkuatan Struktur',
                    'Peningkatan Efisiensi Energi',
                    'Pembaharuan Utilitas'
                ]
            ],
            [
                'icon' => 'couch',
                'title' => 'Desain Interior',
                'description' => 'Layanan desain interior mencakup perencanaan ruang dalam yang memadukan estetika, kenyamanan, dan fungsi. Kami menangani seluruh proses mulai dari konsep hingga pelaksanaan dan styling.',
                'features' => [
                    'Space Planning',
                    'Seleksi Material dan Furnitur',
                    'Desain Pencahayaan',
                    'Custom Furniture Design',
                    'Styling dan Dekorasi'
                ]
            ],
            [
                'icon' => 'drafting-compass',
                'title' => 'Konsultasi Arsitektur',
                'description' => 'Layanan konsultasi untuk membantu klien memahami kebutuhan arsitektural mereka dan memberikan solusi yang tepat untuk masalah bangunan atau perencanaan.',
                'features' => [
                    'Studi Kelayakan',
                    'Analisis Lokasi dan Tapak',
                    'Building Code Compliance',
                    'Konsultasi Struktur',
                    'Perencanaan Masterplan'
                ]
            ],
            [
                'icon' => 'leaf',
                'title' => 'Green Building Design',
                'description' => 'Layanan desain bangunan ramah lingkungan yang mengintegrasikan prinsip-prinsip keberlanjutan untuk menciptakan bangunan yang efisien energi dan minim dampak lingkungan.',
                'features' => [
                    'Desain Pasif untuk Efisiensi Energi',
                    'Sistem Pengolahan Air Hujan',
                    'Seleksi Material Ramah Lingkungan',
                    'Integrasi Sistem Energi Terbarukan',
                    'Sertifikasi Bangunan Hijau'
                ]
            ]
        ];
        
        view('pages.services', [
            'services' => $services
        ]);
    }
    
    /**
     * Display the portfolio page
     */
    public function portfolio()
    {
        $categories = ['All', 'Residential', 'Commercial', 'Public', 'Interior'];
        
        $projects = [
            [
                'image' => 'project-1.jpg',
                'title' => 'Rumah Minimalis Kebayoran',
                'category' => 'Residential',
                'description' => 'Rumah modern minimalis dengan konsep terbuka dan pencahayaan alami maksimal',
                'year' => '2024',
                'location' => 'Kebayoran Baru, Jakarta Selatan',
                'link' => url('/portfolio/rumah-minimalis-kebayoran')
            ],
            [
                'image' => 'project-2.jpg',
                'title' => 'Office Complex Sudirman',
                'category' => 'Commercial',
                'description' => 'Gedung perkantoran modern dengan fasad kaca dan sistem otomasi pintar',
                'year' => '2023',
                'location' => 'Sudirman, Jakarta Pusat',
                'link' => url('/portfolio/office-complex-sudirman')
            ],
            [
                'image' => 'project-3.jpg',
                'title' => 'Villa Puncak',
                'category' => 'Residential',
                'description' => 'Villa bergaya kontemporer dengan pemandangan pegunungan yang spektakuler',
                'year' => '2023',
                'location' => 'Puncak, Bogor',
                'link' => url('/portfolio/villa-puncak')
            ],
            [
                'image' => 'project-4.jpg',
                'title' => 'Interior Apartment Kemang Village',
                'category' => 'Interior',
                'description' => 'Desain interior apartemen mewah dengan gaya modern klasik',
                'year' => '2022',
                'location' => 'Kemang, Jakarta Selatan',
                'link' => url('/portfolio/interior-apartment-kemang')
            ],
            [
                'image' => 'project-5.jpg',
                'title' => 'Sekolah Dasar Cahaya Ilmu',
                'category' => 'Public',
                'description' => 'Kompleks sekolah ramah lingkungan dengan banyak ruang terbuka',
                'year' => '2022',
                'location' => 'Depok, Jawa Barat',
                'link' => url('/portfolio/sekolah-cahaya-ilmu')
            ],
            [
                'image' => 'project-6.jpg',
                'title' => 'Cluster Griya Asri',
                'category' => 'Residential',
                'description' => 'Pengembangan perumahan cluster dengan 25 unit rumah modern tropis',
                'year' => '2021',
                'location' => 'Bekasi, Jawa Barat',
                'link' => url('/portfolio/cluster-griya-asri')
            ],
            [
                'image' => 'project-7.jpg',
                'title' => 'Kantor Startup Digital',
                'category' => 'Commercial',
                'description' => 'Kantor startup dengan open space dan area kolaborasi yang menarik',
                'year' => '2021',
                'location' => 'SCBD, Jakarta Selatan',
                'link' => url('/portfolio/kantor-startup-digital')
            ],
            [
                'image' => 'project-8.jpg',
                'title' => 'Interior Restaurant Bayside',
                'category' => 'Interior',
                'description' => 'Desain interior restoran dengan tema laut dan pemandangan pantai',
                'year' => '2020',
                'location' => 'Ancol, Jakarta Utara',
                'link' => url('/portfolio/interior-restaurant-bayside')
            ],
            [
                'image' => 'project-9.jpg',
                'title' => 'Perpustakaan Kota',
                'category' => 'Public',
                'description' => 'Perpustakaan publik dengan desain modern dan fasilitas digital',
                'year' => '2020',
                'location' => 'Tangerang, Banten',
                'link' => url('/portfolio/perpustakaan-kota')
            ]
        ];
        
        view('pages.portfolio', [
            'categories' => $categories,
            'projects' => $projects
        ]);
    }
    
    /**
     * Display the contact page
     */
    public function contact()
    {
        $contactInfo = [
            'address' => COMPANY_ADDRESS,
            'email' => COMPANY_EMAIL,
            'phone' => COMPANY_PHONE,
            'hours' => COMPANY_HOURS,
            'mapLocation' => [
                'lat' => '-6.2088',
                'lng' => '106.8456',
                'zoom' => 15
            ]
        ];
        
        view('pages.contact', [
            'contactInfo' => $contactInfo
        ]);
    }
}

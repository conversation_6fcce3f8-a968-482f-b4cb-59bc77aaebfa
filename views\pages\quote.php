<?php
/**
 * Quote request page view
 */

// Capture the content for the layout
ob_start();
?>

<!-- Hero Section -->
<section class="relative py-20 bg-cover bg-center" style="background-image: url('<?= asset('images/quote-hero.jpg') ?>');">
    <div class="absolute inset-0 bg-primary bg-opacity-80"></div>
    <div class="container mx-auto px-4 z-10 relative">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-serif font-bold text-white mb-4">Minta Penawaran</h1>
            <div class="w-20 h-1 bg-secondary mx-auto mb-6"></div>
            <p class="text-xl text-white/90 max-w-3xl mx-auto">Dapatkan penawaran yang sesuai dengan kebutuhan proyek Anda. Isi formulir di bawah ini dan tim kami akan segera menghubungi Anda.</p>
        </div>
    </div>
</section>

<!-- Quote Form Section -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto bg-gray-50 rounded-lg shadow-md p-8 md:p-12">
            <h2 class="text-3xl font-serif font-bold mb-6">Formulir Permintaan Penawaran</h2>
            <div class="w-20 h-1 bg-secondary mb-8"></div>
            
            <?php if (isset($success)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-8">
                    <p>Terima kasih telah mengirimkan permintaan penawaran. Tim kami akan segera menghubungi Anda untuk diskusi lebih lanjut.</p>
                </div>
            <?php endif; ?>
            
            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-8">
                    <ul class="list-disc list-inside">
                        <?php foreach ($errors as $error): ?>
                            <li><?= $error ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form action="<?= url('/quote/submit') ?>" method="post" enctype="multipart/form-data" class="space-y-8">
                <!-- Personal Information -->
                <div>
                    <h3 class="text-xl font-medium mb-4">Informasi Pribadi</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-gray-700 font-medium mb-2">Nama Lengkap <span class="text-red-500">*</span></label>
                            <input type="text" id="name" name="name" value="<?= isset($old['name']) ? $old['name'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['name']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        
                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-gray-700 font-medium mb-2">Email <span class="text-red-500">*</span></label>
                            <input type="email" id="email" name="email" value="<?= isset($old['email']) ? $old['email'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['email']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        
                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-gray-700 font-medium mb-2">Telepon <span class="text-red-500">*</span></label>
                            <input type="tel" id="phone" name="phone" value="<?= isset($old['phone']) ? $old['phone'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['phone']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        
                        <!-- Company -->
                        <div>
                            <label for="company" class="block text-gray-700 font-medium mb-2">Perusahaan (opsional)</label>
                            <input type="text" id="company" name="company" value="<?= isset($old['company']) ? $old['company'] : '' ?>" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                    </div>
                </div>
                
                <!-- Project Information -->
                <div>
                    <h3 class="text-xl font-medium mb-4">Informasi Proyek</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Project Type -->
                        <div>
                            <label for="project_type" class="block text-gray-700 font-medium mb-2">Jenis Proyek <span class="text-red-500">*</span></label>
                            <select id="project_type" name="project_type" required class="w-full px-4 py-3 border <?= isset($errors['project_type']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                <option value="">Pilih Jenis Proyek</option>
                                <option value="residential" <?= (isset($old['project_type']) && $old['project_type'] === 'residential') ? 'selected' : '' ?>>Residensial</option>
                                <option value="commercial" <?= (isset($old['project_type']) && $old['project_type'] === 'commercial') ? 'selected' : '' ?>>Komersial</option>
                                <option value="interior" <?= (isset($old['project_type']) && $old['project_type'] === 'interior') ? 'selected' : '' ?>>Desain Interior</option>
                                <option value="renovation" <?= (isset($old['project_type']) && $old['project_type'] === 'renovation') ? 'selected' : '' ?>>Renovasi</option>
                                <option value="other" <?= (isset($old['project_type']) && $old['project_type'] === 'other') ? 'selected' : '' ?>>Lainnya</option>
                            </select>
                        </div>
                        
                        <!-- Other Project Type (conditional) -->
                        <div id="other_project_type_container" class="<?= (isset($old['project_type']) && $old['project_type'] === 'other') ? '' : 'hidden' ?>">
                            <label for="other_project_type" class="block text-gray-700 font-medium mb-2">Sebutkan Jenis Proyek <span class="text-red-500">*</span></label>
                            <input type="text" id="other_project_type" name="other_project_type" value="<?= isset($old['other_project_type']) ? $old['other_project_type'] : '' ?>" class="w-full px-4 py-3 border <?= isset($errors['other_project_type']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        
                        <!-- Location -->
                        <div>
                            <label for="location" class="block text-gray-700 font-medium mb-2">Lokasi Proyek <span class="text-red-500">*</span></label>
                            <input type="text" id="location" name="location" value="<?= isset($old['location']) ? $old['location'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['location']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                        
                        <!-- Estimated Budget Range -->
                        <div>
                            <label for="budget" class="block text-gray-700 font-medium mb-2">Perkiraan Anggaran <span class="text-red-500">*</span></label>
                            <select id="budget" name="budget" required class="w-full px-4 py-3 border <?= isset($errors['budget']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                <option value="">Pilih Kisaran Anggaran</option>
                                <option value="< 100 juta" <?= (isset($old['budget']) && $old['budget'] === '< 100 juta') ? 'selected' : '' ?>>< Rp 100 juta</option>
                                <option value="100-500 juta" <?= (isset($old['budget']) && $old['budget'] === '100-500 juta') ? 'selected' : '' ?>>Rp 100 juta - Rp 500 juta</option>
                                <option value="500 juta - 1 miliar" <?= (isset($old['budget']) && $old['budget'] === '500 juta - 1 miliar') ? 'selected' : '' ?>>Rp 500 juta - Rp 1 miliar</option>
                                <option value="1-5 miliar" <?= (isset($old['budget']) && $old['budget'] === '1-5 miliar') ? 'selected' : '' ?>>Rp 1 miliar - Rp 5 miliar</option>
                                <option value="> 5 miliar" <?= (isset($old['budget']) && $old['budget'] === '> 5 miliar') ? 'selected' : '' ?>>> Rp 5 miliar</option>
                            </select>
                        </div>
                        
                        <!-- Timeframe -->
                        <div>
                            <label for="timeframe" class="block text-gray-700 font-medium mb-2">Kerangka Waktu <span class="text-red-500">*</span></label>
                            <select id="timeframe" name="timeframe" required class="w-full px-4 py-3 border <?= isset($errors['timeframe']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                                <option value="">Pilih Kerangka Waktu</option>
                                <option value="ASAP" <?= (isset($old['timeframe']) && $old['timeframe'] === 'ASAP') ? 'selected' : '' ?>>Segera</option>
                                <option value="1-3 bulan" <?= (isset($old['timeframe']) && $old['timeframe'] === '1-3 bulan') ? 'selected' : '' ?>>1-3 bulan</option>
                                <option value="3-6 bulan" <?= (isset($old['timeframe']) && $old['timeframe'] === '3-6 bulan') ? 'selected' : '' ?>>3-6 bulan</option>
                                <option value="6-12 bulan" <?= (isset($old['timeframe']) && $old['timeframe'] === '6-12 bulan') ? 'selected' : '' ?>>6-12 bulan</option>
                                <option value="> 12 bulan" <?= (isset($old['timeframe']) && $old['timeframe'] === '> 12 bulan') ? 'selected' : '' ?>>> 12 bulan</option>
                            </select>
                        </div>
                        
                        <!-- Area Size -->
                        <div>
                            <label for="area_size" class="block text-gray-700 font-medium mb-2">Luas Area (m²) <span class="text-red-500">*</span></label>
                            <input type="number" id="area_size" name="area_size" value="<?= isset($old['area_size']) ? $old['area_size'] : '' ?>" required class="w-full px-4 py-3 border <?= isset($errors['area_size']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        </div>
                    </div>
                </div>
                
                <!-- Project Details -->
                <div>
                    <h3 class="text-xl font-medium mb-4">Detail Proyek</h3>
                    
                    <!-- Project Description -->
                    <div class="mb-6">
                        <label for="description" class="block text-gray-700 font-medium mb-2">Deskripsi Proyek <span class="text-red-500">*</span></label>
                        <textarea id="description" name="description" rows="6" required class="w-full px-4 py-3 border <?= isset($errors['description']) ? 'border-red-500' : 'border-gray-300' ?> rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"><?= isset($old['description']) ? $old['description'] : '' ?></textarea>
                        <p class="text-sm text-gray-500 mt-2">Jelaskan visi, kebutuhan, dan harapan Anda untuk proyek ini.</p>
                    </div>
                    
                    <!-- Services Needed -->
                    <div class="mb-6">
                        <label class="block text-gray-700 font-medium mb-2">Layanan yang Dibutuhkan <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="service_design" name="services[]" value="design" <?= (isset($old['services']) && in_array('design', $old['services'])) ? 'checked' : '' ?> class="h-5 w-5 text-secondary focus:ring-secondary">
                                <label for="service_design" class="ml-2 text-gray-700">Desain Arsitektur</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="service_interior" name="services[]" value="interior" <?= (isset($old['services']) && in_array('interior', $old['services'])) ? 'checked' : '' ?> class="h-5 w-5 text-secondary focus:ring-secondary">
                                <label for="service_interior" class="ml-2 text-gray-700">Desain Interior</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="service_construction" name="services[]" value="construction" <?= (isset($old['services']) && in_array('construction', $old['services'])) ? 'checked' : '' ?> class="h-5 w-5 text-secondary focus:ring-secondary">
                                <label for="service_construction" class="ml-2 text-gray-700">Konstruksi</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="service_renovation" name="services[]" value="renovation" <?= (isset($old['services']) && in_array('renovation', $old['services'])) ? 'checked' : '' ?> class="h-5 w-5 text-secondary focus:ring-secondary">
                                <label for="service_renovation" class="ml-2 text-gray-700">Renovasi</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="service_consultation" name="services[]" value="consultation" <?= (isset($old['services']) && in_array('consultation', $old['services'])) ? 'checked' : '' ?> class="h-5 w-5 text-secondary focus:ring-secondary">
                                <label for="service_consultation" class="ml-2 text-gray-700">Konsultasi</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="service_other" name="services[]" value="other" <?= (isset($old['services']) && in_array('other', $old['services'])) ? 'checked' : '' ?> class="h-5 w-5 text-secondary focus:ring-secondary">
                                <label for="service_other" class="ml-2 text-gray-700">Lainnya</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Reference Files -->
                    <div>
                        <label for="attachments" class="block text-gray-700 font-medium mb-2">Lampiran (opsional)</label>
                        <input type="file" id="attachments" name="attachments[]" multiple class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        <p class="text-sm text-gray-500 mt-2">Upload referensi, sketsa, atau dokumen lain yang dapat membantu kami memahami proyek Anda. Maksimal 5 file (PDF, JPG, PNG).</p>
                    </div>
                </div>
                
                <!-- How did you hear about us? -->
                <div>
                    <label for="referral_source" class="block text-gray-700 font-medium mb-2">Bagaimana Anda mengetahui tentang kami? (opsional)</label>
                    <select id="referral_source" name="referral_source" class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent">
                        <option value="">Pilih Sumber</option>
                        <option value="search" <?= (isset($old['referral_source']) && $old['referral_source'] === 'search') ? 'selected' : '' ?>>Pencarian Google</option>
                        <option value="social_media" <?= (isset($old['referral_source']) && $old['referral_source'] === 'social_media') ? 'selected' : '' ?>>Media Sosial</option>
                        <option value="referral" <?= (isset($old['referral_source']) && $old['referral_source'] === 'referral') ? 'selected' : '' ?>>Rekomendasi Teman/Keluarga</option>
                        <option value="previous_client" <?= (isset($old['referral_source']) && $old['referral_source'] === 'previous_client') ? 'selected' : '' ?>>Klien Sebelumnya</option>
                        <option value="advertisement" <?= (isset($old['referral_source']) && $old['referral_source'] === 'advertisement') ? 'selected' : '' ?>>Iklan</option>
                        <option value="other" <?= (isset($old['referral_source']) && $old['referral_source'] === 'other') ? 'selected' : '' ?>>Lainnya</option>
                    </select>
                </div>
                
                <!-- Terms and Conditions -->
                <div>
                    <div class="flex items-start">
                        <input type="checkbox" id="terms" name="terms" required <?= (isset($old['terms']) && $old['terms']) ? 'checked' : '' ?> class="h-5 w-5 mt-1 text-secondary focus:ring-secondary <?= isset($errors['terms']) ? 'border-red-500' : '' ?>">
                        <label for="terms" class="ml-2 text-gray-700">
                            Saya menyetujui <a href="#" class="text-secondary hover:underline">Syarat dan Ketentuan</a> serta <a href="#" class="text-secondary hover:underline">Kebijakan Privasi</a> Antosa Architect <span class="text-red-500">*</span>
                        </label>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div>
                    <button type="submit" class="inline-block bg-secondary hover:bg-primary text-white font-medium py-3 px-8 rounded-md transition-colors text-lg">Kirim Permintaan Penawaran</button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- JavaScript for form conditionals -->
<?php
$extraScripts = <<<EOT
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const projectTypeSelect = document.getElementById('project_type');
        const otherProjectTypeContainer = document.getElementById('other_project_type_container');
        
        // Show/hide other project type field based on selection
        projectTypeSelect.addEventListener('change', function() {
            if (this.value === 'other') {
                otherProjectTypeContainer.classList.remove('hidden');
            } else {
                otherProjectTypeContainer.classList.add('hidden');
            }
        });
    });
</script>
EOT;
?>

<?php
// Set the content variable for the layout
$content = ob_get_clean();

// Set page-specific variables
$pageTitle = 'Minta Penawaran';
$pageDescription = 'Isi formulir untuk mendapatkan penawaran proyek arsitektur dan konstruksi dari Antosa Architect';

// Load the layout
require_once VIEWS_PATH . '/layouts/main.php';
?>

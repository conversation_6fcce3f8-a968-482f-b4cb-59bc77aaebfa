<?php
/**
 * Quote Controller
 * Handles quote/contact form submissions
 */
class QuoteController
{
    /**
     * Display the quote request form page
     */
    public function index()
    {
        $serviceTypes = [
            'Desain Arsitektur',
            'Konstruksi Bangunan',
            'Renovasi dan <PERSON>',
            'Desain Interior',
            'Konsultasi Arsitektur',
            'Green Building Design'
        ];
        
        $projectTypes = [
            'Residensial',
            'Komersial',
            'Perkantoran',
            'Institusi Pendidikan',
            'Fasilitas Publik',
            'Lainnya'
        ];
        
        $budgetRanges = [
            'Dibawah Rp 100 Juta',
            'Rp 100 Juta - Rp 500 Juta',
            'Rp 500 Juta - Rp 1 Miliar',
            'Rp 1 Miliar - Rp 5 Miliar',
            'Diatas Rp 5 Miliar',
            'Belum ditentukan'
        ];
        
        view('pages.quote', [
            'serviceTypes' => $serviceTypes,
            'projectTypes' => $projectTypes,
            'budgetRanges' => $budgetRanges
        ]);
    }
    
    /**
     * Process the quote request submission
     */
    public function submit()
    {
        // Validate form input
        $errors = [];
        
        // Required fields
        $requiredFields = [
            'name' => 'Nama',
            'email' => 'Email',
            'phone' => 'Nomor Telepon',
            'service_type' => 'Jenis Layanan',
            'project_type' => 'Jenis Proyek',
            'message' => 'Deskripsi Proyek'
        ];
        
        // Check for empty required fields
        foreach ($requiredFields as $field => $label) {
            if (empty($_POST[$field])) {
                $errors[] = "{$label} tidak boleh kosong";
            }
        }
        
        // Validate email format
        if (!empty($_POST['email']) && !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Format email tidak valid";
        }
        
        // Validate phone format (simple validation)
        if (!empty($_POST['phone']) && !preg_match('/^[0-9+\-\s()]{10,20}$/', $_POST['phone'])) {
            $errors[] = "Format nomor telepon tidak valid";
        }
        
        // If there are errors, show the form again with error messages
        if (!empty($errors)) {
            $serviceTypes = [
                'Desain Arsitektur',
                'Konstruksi Bangunan',
                'Renovasi dan Restorasi',
                'Desain Interior',
                'Konsultasi Arsitektur',
                'Green Building Design'
            ];
            
            $projectTypes = [
                'Residensial',
                'Komersial',
                'Perkantoran',
                'Institusi Pendidikan',
                'Fasilitas Publik',
                'Lainnya'
            ];
            
            $budgetRanges = [
                'Dibawah Rp 100 Juta',
                'Rp 100 Juta - Rp 500 Juta',
                'Rp 500 Juta - Rp 1 Miliar',
                'Rp 1 Miliar - Rp 5 Miliar',
                'Diatas Rp 5 Miliar',
                'Belum ditentukan'
            ];
            
            view('pages.quote', [
                'errors' => $errors,
                'oldInput' => $_POST,
                'serviceTypes' => $serviceTypes,
                'projectTypes' => $projectTypes,
                'budgetRanges' => $budgetRanges
            ]);
            return;
        }
        
        // Sanitize input data
        $name = sanitize($_POST['name']);
        $email = sanitize($_POST['email']);
        $phone = sanitize($_POST['phone']);
        $serviceType = sanitize($_POST['service_type']);
        $projectType = sanitize($_POST['project_type']);
        $budgetRange = isset($_POST['budget_range']) ? sanitize($_POST['budget_range']) : 'Belum ditentukan';
        $message = sanitize($_POST['message']);
        $timeframe = isset($_POST['timeframe']) ? sanitize($_POST['timeframe']) : '';
        
        // In a real application, you would:
        // 1. Save the form data to a database
        // 2. Send notification email to admin
        // 3. Send confirmation email to user
        
        // For now, we'll just simulate success and redirect to thank you page
        
        // Store quote info in session for thank you page
        session_start();
        $_SESSION['quote_submission'] = [
            'name' => $name,
            'email' => $email,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Redirect to thank you page
        redirect('/quote/thank-you');
    }
    
    /**
     * Display thank you page after successful submission
     * (Add this route in web.php)
     */
    public function thankYou()
    {
        session_start();
        
        // Check if there's submission data in session
        if (!isset($_SESSION['quote_submission'])) {
            redirect('/quote');
            return;
        }
        
        $submission = $_SESSION['quote_submission'];
        
        // Clear session data after using it
        unset($_SESSION['quote_submission']);
        
        view('pages.quote-thank-you', [
            'submission' => $submission
        ]);
    }
}

<?php
/**
 * Helper functions for the application
 */

/**
 * Generate a URL for the application
 * 
 * @param string $path The path to append to the base URL
 * @return string The complete URL
 */
function url($path = '')
{
    return APP_URL . '/' . ltrim($path, '/');
}

/**
 * Include a view file
 * 
 * @param string $view The view name
 * @param array $data Data to pass to the view
 * @return void
 */
function view($view, $data = [])
{
    // Extract data to make variables available in view
    if (!empty($data)) {
        extract($data);
    }
    
    $viewPath = VIEWS_PATH . '/' . str_replace('.', '/', $view) . '.php';
    
    if (file_exists($viewPath)) {
        require $viewPath;
    } else {
        throw new Exception("View {$view} not found");
    }
}

/**
 * Load a component
 * 
 * @param string $component The component name
 * @param array $data Data to pass to the component
 * @return void
 */
function component($component, $data = [])
{
    view('components.' . $component, $data);
}

/**
 * Sanitize input
 * 
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitize($input)
{
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Check if the current page is active
 * 
 * @param string $path The path to check
 * @return bool Whether the path is active
 */
function isActive($path)
{
    $currentPath = $_SERVER['REQUEST_URI'] ?? '/';
    return strpos($currentPath, $path) !== false;
}

/**
 * Generate the asset URL
 * 
 * @param string $path The asset path
 * @return string The complete asset URL
 */
function asset($path)
{
    return url('assets/' . ltrim($path, '/'));
}

/**
 * Redirect to another page
 * 
 * @param string $path The path to redirect to
 * @return void
 */
function redirect($path)
{
    header('Location: ' . url($path));
    exit;
}

/**
 * Get current year for copyright
 * 
 * @return string The current year
 */
function currentYear()
{
    return date('Y');
}
